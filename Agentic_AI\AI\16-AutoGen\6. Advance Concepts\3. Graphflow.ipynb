{"cells": [{"cell_type": "code", "execution_count": null, "id": "7271c3d0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "ff652569", "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_agentchat.teams  import DiGraphBuilder, GraphFlow\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "# Create an OpenAI model client\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "client = OpenAIChatCompletionClient(model=\"gpt-4.1-nano\",api_key=api_key)\n", "\n", "# Create the writer agent\n", "writer = AssistantAgent(\"writer\", model_client=client, system_message=\"Draft a short paragraph on climate change.\")\n", "\n", "# Create the reviewer agent\n", "reviewer = AssistantAgent(\"reviewer\", model_client=client, system_message=\"Review the draft and suggest improvements.\")\n", "\n", "# Build the graph\n", "builder = DiGraphBuilder()\n", "builder.add_node(writer).add_node(reviewer)\n", "builder.add_edge(writer, reviewer)\n", "\n", "# Build and validate the graph\n", "graph = builder.build()\n", "\n", "# Create the flow\n", "flow = GraphFlow([writer, reviewer], graph=graph)\n"]}, {"cell_type": "code", "execution_count": null, "id": "b6628609", "metadata": {}, "outputs": [], "source": ["builder.add_node"]}, {"cell_type": "code", "execution_count": 9, "id": "fb3bec3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["source='user' models_usage=None metadata={} created_at=datetime.datetime(2025, 6, 15, 11, 56, 35, 364977, tzinfo=datetime.timezone.utc) content='Write a short paragraph about climate change.' type='TextMessage'\n", "source='writer' models_usage=RequestUsage(prompt_tokens=28, completion_tokens=110) metadata={} created_at=datetime.datetime(2025, 6, 15, 11, 56, 37, 87884, tzinfo=datetime.timezone.utc) content='Climate change refers to long-term shifts in temperature, weather patterns, and other climate variables primarily caused by human activities such as burning fossil fuels, deforestation, and industrial processes. These activities increase the concentration of greenhouse gases like carbon dioxide in the atmosphere, leading to global warming. The consequences of climate change include more frequent and severe storms, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing climate change requires global cooperation to reduce emissions, shift to renewable energy sources, and implement sustainable practices to protect the environment for future generations.' type='TextMessage'\n", "source='reviewer' models_usage=RequestUsage(prompt_tokens=143, completion_tokens=155) metadata={} created_at=datetime.datetime(2025, 6, 15, 11, 56, 38, 308354, tzinfo=datetime.timezone.utc) content=\"Your paragraph provides a clear and comprehensive overview of climate change. To enhance readability and engagement, consider incorporating more varied sentence structures and a touch of urgency to emphasize the importance of action. Here's a revised version:\\n\\nClimate change involves long-term alterations in temperature, weather patterns, and other climate variables driven largely by human activities such as burning fossil fuels, deforestation, and industrial processes. These actions increase greenhouse gas levels—particularly carbon dioxide—in the atmosphere, leading to global warming. The impacts are profound and far-reaching, including more intense storms, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this urgent issue requires global cooperation to reduce emissions, transition to renewable energy sources, and adopt sustainable practices—ensuring a healthier planet for current and future generations.\" type='TextMessage'\n", "source='DiGraphStopAgent' models_usage=None metadata={} created_at=datetime.datetime(2025, 6, 15, 11, 56, 38, 309415, tzinfo=datetime.timezone.utc) content='Digraph execution is complete' type='StopMessage'\n", "messages=[TextMessage(source='user', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 6, 15, 11, 56, 35, 364977, tzinfo=datetime.timezone.utc), content='Write a short paragraph about climate change.', type='TextMessage'), TextMessage(source='writer', models_usage=RequestUsage(prompt_tokens=28, completion_tokens=110), metadata={}, created_at=datetime.datetime(2025, 6, 15, 11, 56, 37, 87884, tzinfo=datetime.timezone.utc), content='Climate change refers to long-term shifts in temperature, weather patterns, and other climate variables primarily caused by human activities such as burning fossil fuels, deforestation, and industrial processes. These activities increase the concentration of greenhouse gases like carbon dioxide in the atmosphere, leading to global warming. The consequences of climate change include more frequent and severe storms, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing climate change requires global cooperation to reduce emissions, shift to renewable energy sources, and implement sustainable practices to protect the environment for future generations.', type='TextMessage'), TextMessage(source='reviewer', models_usage=RequestUsage(prompt_tokens=143, completion_tokens=155), metadata={}, created_at=datetime.datetime(2025, 6, 15, 11, 56, 38, 308354, tzinfo=datetime.timezone.utc), content=\"Your paragraph provides a clear and comprehensive overview of climate change. To enhance readability and engagement, consider incorporating more varied sentence structures and a touch of urgency to emphasize the importance of action. Here's a revised version:\\n\\nClimate change involves long-term alterations in temperature, weather patterns, and other climate variables driven largely by human activities such as burning fossil fuels, deforestation, and industrial processes. These actions increase greenhouse gas levels—particularly carbon dioxide—in the atmosphere, leading to global warming. The impacts are profound and far-reaching, including more intense storms, rising sea levels, melting glaciers, and disruptions to ecosystems and agriculture. Addressing this urgent issue requires global cooperation to reduce emissions, transition to renewable energy sources, and adopt sustainable practices—ensuring a healthier planet for current and future generations.\", type='TextMessage'), StopMessage(source='DiGraphStopAgent', models_usage=None, metadata={}, created_at=datetime.datetime(2025, 6, 15, 11, 56, 38, 309415, tzinfo=datetime.timezone.utc), content='Digraph execution is complete', type='StopMessage')] stop_reason='Stop message received'\n"]}], "source": ["# Use `asyncio.run(...)` and wrap the below in a async function when running in a script.\n", "stream = flow.run_stream(task=\"Write a short paragraph about climate change.\")\n", "async for event in stream:  # type: ignore\n", "    print(event)\n", "# Use Console(flow.run_stream(...)) for better formatting in console.\n"]}, {"cell_type": "code", "execution_count": null, "id": "86c0cc45", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f15d0732", "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent, MessageFilterAgent, MessageFilterConfig, PerSourceFilter\n", "from autogen_agentchat.teams import DiGraphBuilder, GraphFlow\n", "from autogen_agentchat.ui import Console\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "\n", "# Model client\n", "client = OpenAIChatCompletionClient(model=\"gpt-4.1-nano\")\n", "\n", "# Create agents\n", "researcher = AssistantAgent(\n", "    \"researcher\", model_client=client, system_message=\"Summarize key facts about climate change.\"\n", ")\n", "analyst = AssistantAgent(\"analyst\", model_client=client, system_message=\"Review the summary and suggest improvements.\")\n", "presenter = Assistant<PERSON><PERSON>(\n", "    \"presenter\", model_client=client, system_message=\"Prepare a presentation slide based on the final summary.\"\n", ")\n", "\n", "# Apply message filtering\n", "filtered_analyst = MessageFilterAgent(\n", "    name=\"analyst\",\n", "    wrapped_agent=analyst,\n", "    filter=MessageFilterConfig(per_source=[PerSourceFilter(source=\"researcher\", position=\"last\", count=1)]),\n", ")\n", "\n", "filtered_presenter = MessageFilterAgent(\n", "    name=\"presenter\",\n", "    wrapped_agent=presenter,\n", "    filter=MessageFilterConfig(per_source=[PerSourceFilter(source=\"analyst\", position=\"last\", count=1)]),\n", ")\n", "\n", "# Build the flow\n", "builder = DiGraphBuilder()\n", "builder.add_node(researcher).add_node(filtered_analyst).add_node(filtered_presenter)\n", "builder.add_edge(researcher, filtered_analyst).add_edge(filtered_analyst, filtered_presenter)\n", "\n", "# Create the flow\n", "flow = GraphFlow(\n", "    participants=builder.get_participants(),\n", "    graph=builder.build(),\n", ")\n", "\n", "# Run the flow\n", "await Console(flow.run_stream(task=\"Summarize key facts about climate change.\"))\n"]}], "metadata": {"kernelspec": {"display_name": "venv-autogen", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}