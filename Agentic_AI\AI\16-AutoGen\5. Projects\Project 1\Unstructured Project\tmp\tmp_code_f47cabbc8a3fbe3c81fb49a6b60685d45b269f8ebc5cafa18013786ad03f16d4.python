import matplotlib.pyplot as plt

# Function to check if a number is prime
def is_prime(n):
    if n <= 1:
        return False
    for i in range(2, int(n**0.5) + 1):
        if n % i == 0:
            return False
    return True

# Check for prime numbers from 1 to 15
numbers = range(1, 16)
prime_status = [is_prime(n) for n in numbers]

# Print results for each number
print("Primality Check from 1 to 15:")
for number, prime in zip(numbers, prime_status):
    result = 'Prime' if prime else 'Not Prime'
    print(f"{number}: {result}")

# Plotting
plt.bar(numbers, prime_status, color=['blue' if prime else 'red' for prime in prime_status])
plt.xlabel('Numbers')
plt.ylabel('Is Prime (1=True, 0=False)')
plt.title('Prime Numbers from 1 to 15')
plt.xticks(numbers)
plt.yticks([0, 1], ['Not Prime', 'Prime'])
plt.grid(axis='y')
plt.savefig('output.png')
plt.show()
