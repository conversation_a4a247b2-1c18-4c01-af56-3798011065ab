{"cells": [{"cell_type": "markdown", "id": "a3d0b267", "metadata": {}, "source": ["# Autogen Installation"]}, {"cell_type": "code", "execution_count": null, "id": "2daa6db6", "metadata": {}, "outputs": [], "source": ["1. Create a virtual env\n", "2. Activate it\n", "3. Create Requirements.txt\n", "4. install them\n"]}, {"cell_type": "markdown", "id": "219b3c84", "metadata": {}, "source": ["# LLM for our Agents\n", "\n", "1. ChatGPT\n", "2. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 4, "id": "6ba3adea", "metadata": {}, "outputs": [], "source": ["from autogen_core.models import UserMessage\n", "from autogen_ext.models.ollama import OllamaChatCompletionClient\n", "import asyncio\n", "\n", "ollama_model_client = OllamaChatCompletionClient(model='llama3.2')"]}, {"cell_type": "code", "execution_count": 6, "id": "29248e93", "metadata": {}, "outputs": [{"data": {"text/plain": ["<coroutine object main at 0x109688c80>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["async def main():\n", "    response = await ollama_model_client.create(UserMessage(content='Hello, how are you?',source='user'))\n", "    print(response)\n", "    await ollama_model_client.close()\n", "\n", "main()"]}, {"cell_type": "markdown", "id": "b15ffff3", "metadata": {}, "source": ["# First Autogen Agent"]}, {"cell_type": "code", "execution_count": 1, "id": "3c97cadb", "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "from dotenv import load_dotenv\n", "import os\n", "load_dotenv()\n", "api_key = os.getenv('OPENAI_API_KEY')\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c7fe94dc", "metadata": {}, "outputs": [], "source": ["model_client = OpenAIChatCompletionClient(model='gpt-4o',api_key=api_key)"]}, {"cell_type": "markdown", "id": "67f31934", "metadata": {}, "source": ["# Creating our First Single Agent"]}, {"cell_type": "code", "execution_count": 3, "id": "65f602d1", "metadata": {}, "outputs": [], "source": ["my_first_agent = AssistantAgent(name = 'My_First_Agent',model_client=model_client,system_message='You are a helpful assistant, give reply in JSON')"]}, {"cell_type": "code", "execution_count": 16, "id": "5713d493", "metadata": {}, "outputs": [{"data": {"text/plain": ["'********************************************************************************************************************************************************************'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["api_key"]}, {"cell_type": "code", "execution_count": 6, "id": "2261b7d0", "metadata": {}, "outputs": [{"ename": "AuthenticationError", "evalue": "Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-proj-********************************************************************************************************************************************************qZkA. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAuthenticationError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m result = \u001b[38;5;28;01mawait\u001b[39;00m my_first_agent.run(task=\u001b[33m'\u001b[39m\u001b[33mTell me a joke\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(result)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/autogen_agentchat/agents/_base_chat_agent.py:136\u001b[39m, in \u001b[36mBaseChatAgent.run\u001b[39m\u001b[34m(self, task, cancellation_token)\u001b[39m\n\u001b[32m    134\u001b[39m         \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    135\u001b[39m             \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mInvalid message type in sequence: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(msg)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m response = \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m.on_messages(input_messages, cancellation_token)\n\u001b[32m    137\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m response.inner_messages \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m:\n\u001b[32m    138\u001b[39m     output_messages += response.inner_messages\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py:782\u001b[39m, in \u001b[36mAssistantAgent.on_messages\u001b[39m\u001b[34m(self, messages, cancellation_token)\u001b[39m\n\u001b[32m    781\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mon_messages\u001b[39m(\u001b[38;5;28mself\u001b[39m, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken) -> Response:\n\u001b[32m--> \u001b[39m\u001b[32m782\u001b[39m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m message \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.on_messages_stream(messages, cancellation_token):\n\u001b[32m    783\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(message, Response):\n\u001b[32m    784\u001b[39m             \u001b[38;5;28;01mreturn\u001b[39;00m message\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py:827\u001b[39m, in \u001b[36mAssistantAgent.on_messages_stream\u001b[39m\u001b[34m(self, messages, cancellation_token)\u001b[39m\n\u001b[32m    825\u001b[39m \u001b[38;5;66;03m# STEP 3: Run the first inference\u001b[39;00m\n\u001b[32m    826\u001b[39m model_result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m827\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m inference_output \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._call_llm(\n\u001b[32m    828\u001b[39m     model_client=model_client,\n\u001b[32m    829\u001b[39m     model_client_stream=model_client_stream,\n\u001b[32m    830\u001b[39m     system_messages=system_messages,\n\u001b[32m    831\u001b[39m     model_context=model_context,\n\u001b[32m    832\u001b[39m     workbench=workbench,\n\u001b[32m    833\u001b[39m     handoff_tools=handoff_tools,\n\u001b[32m    834\u001b[39m     agent_name=agent_name,\n\u001b[32m    835\u001b[39m     cancellation_token=cancellation_token,\n\u001b[32m    836\u001b[39m     output_content_type=output_content_type,\n\u001b[32m    837\u001b[39m ):\n\u001b[32m    838\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(inference_output, CreateResult):\n\u001b[32m    839\u001b[39m         model_result = inference_output\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/autogen_agentchat/agents/_assistant_agent.py:955\u001b[39m, in \u001b[36mAssistantAgent._call_llm\u001b[39m\u001b[34m(cls, model_client, model_client_stream, system_messages, model_context, workbench, handoff_tools, agent_name, cancellation_token, output_content_type)\u001b[39m\n\u001b[32m    953\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m model_result\n\u001b[32m    954\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m955\u001b[39m     model_result = \u001b[38;5;28;01mawait\u001b[39;00m model_client.create(\n\u001b[32m    956\u001b[39m         llm_messages,\n\u001b[32m    957\u001b[39m         tools=tools,\n\u001b[32m    958\u001b[39m         cancellation_token=cancellation_token,\n\u001b[32m    959\u001b[39m         json_output=output_content_type,\n\u001b[32m    960\u001b[39m     )\n\u001b[32m    961\u001b[39m     \u001b[38;5;28;<PERSON><PERSON><PERSON>\u001b[39;00m model_result\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/autogen_ext/models/openai/_openai_client.py:624\u001b[39m, in \u001b[36mBaseOpenAIChatCompletionClient.create\u001b[39m\u001b[34m(self, messages, tools, json_output, extra_create_args, cancellation_token)\u001b[39m\n\u001b[32m    622\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m cancellation_token \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    623\u001b[39m     cancellation_token.link_future(future)\n\u001b[32m--> \u001b[39m\u001b[32m624\u001b[39m result: Union[ParsedChatCompletion[BaseModel], ChatCompletion] = \u001b[38;5;28;01mawait\u001b[39;00m future\n\u001b[32m    625\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m create_params.response_format \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    626\u001b[39m     result = cast(ParsedChatCompletion[Any], result)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/openai/resources/chat/completions/completions.py:2028\u001b[39m, in \u001b[36mAsyncCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m   1985\u001b[39m \u001b[38;5;129m@required_args\u001b[39m([\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m], [\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m   1986\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m   1987\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   2025\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m   2026\u001b[39m ) -> ChatCompletion | AsyncStream[ChatCompletionChunk]:\n\u001b[32m   2027\u001b[39m     validate_response_format(response_format)\n\u001b[32m-> \u001b[39m\u001b[32m2028\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m._post(\n\u001b[32m   2029\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m/chat/completions\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   2030\u001b[39m         body=\u001b[38;5;28;01mawait\u001b[39;00m async_maybe_transform(\n\u001b[32m   2031\u001b[39m             {\n\u001b[32m   2032\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m: messages,\n\u001b[32m   2033\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m: model,\n\u001b[32m   2034\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33maudio\u001b[39m\u001b[33m\"\u001b[39m: audio,\n\u001b[32m   2035\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mfrequency_penalty\u001b[39m\u001b[33m\"\u001b[39m: frequency_penalty,\n\u001b[32m   2036\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mfunction_call\u001b[39m\u001b[33m\"\u001b[39m: function_call,\n\u001b[32m   2037\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mfunctions\u001b[39m\u001b[33m\"\u001b[39m: functions,\n\u001b[32m   2038\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mlogit_bias\u001b[39m\u001b[33m\"\u001b[39m: logit_bias,\n\u001b[32m   2039\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mlogprobs\u001b[39m\u001b[33m\"\u001b[39m: logprobs,\n\u001b[32m   2040\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmax_completion_tokens\u001b[39m\u001b[33m\"\u001b[39m: max_completion_tokens,\n\u001b[32m   2041\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmax_tokens\u001b[39m\u001b[33m\"\u001b[39m: max_tokens,\n\u001b[32m   2042\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmetadata\u001b[39m\u001b[33m\"\u001b[39m: metadata,\n\u001b[32m   2043\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmodalities\u001b[39m\u001b[33m\"\u001b[39m: modalities,\n\u001b[32m   2044\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mn\u001b[39m\u001b[33m\"\u001b[39m: n,\n\u001b[32m   2045\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mparallel_tool_calls\u001b[39m\u001b[33m\"\u001b[39m: parallel_tool_calls,\n\u001b[32m   2046\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mprediction\u001b[39m\u001b[33m\"\u001b[39m: prediction,\n\u001b[32m   2047\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mpresence_penalty\u001b[39m\u001b[33m\"\u001b[39m: presence_penalty,\n\u001b[32m   2048\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mreasoning_effort\u001b[39m\u001b[33m\"\u001b[39m: reasoning_effort,\n\u001b[32m   2049\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mresponse_format\u001b[39m\u001b[33m\"\u001b[39m: response_format,\n\u001b[32m   2050\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mseed\u001b[39m\u001b[33m\"\u001b[39m: seed,\n\u001b[32m   2051\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mservice_tier\u001b[39m\u001b[33m\"\u001b[39m: service_tier,\n\u001b[32m   2052\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstop\u001b[39m\u001b[33m\"\u001b[39m: stop,\n\u001b[32m   2053\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstore\u001b[39m\u001b[33m\"\u001b[39m: store,\n\u001b[32m   2054\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m: stream,\n\u001b[32m   2055\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstream_options\u001b[39m\u001b[33m\"\u001b[39m: stream_options,\n\u001b[32m   2056\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtemperature\u001b[39m\u001b[33m\"\u001b[39m: temperature,\n\u001b[32m   2057\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtool_choice\u001b[39m\u001b[33m\"\u001b[39m: tool_choice,\n\u001b[32m   2058\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtools\u001b[39m\u001b[33m\"\u001b[39m: tools,\n\u001b[32m   2059\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtop_logprobs\u001b[39m\u001b[33m\"\u001b[39m: top_logprobs,\n\u001b[32m   2060\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtop_p\u001b[39m\u001b[33m\"\u001b[39m: top_p,\n\u001b[32m   2061\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33muser\u001b[39m\u001b[33m\"\u001b[39m: user,\n\u001b[32m   2062\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mweb_search_options\u001b[39m\u001b[33m\"\u001b[39m: web_search_options,\n\u001b[32m   2063\u001b[39m             },\n\u001b[32m   2064\u001b[39m             completion_create_params.CompletionCreateParamsStreaming\n\u001b[32m   2065\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m stream\n\u001b[32m   2066\u001b[39m             \u001b[38;5;28;01melse\u001b[39;00m completion_create_params.CompletionCreateParamsNonStreaming,\n\u001b[32m   2067\u001b[39m         ),\n\u001b[32m   2068\u001b[39m         options=make_request_options(\n\u001b[32m   2069\u001b[39m             extra_headers=extra_headers, extra_query=extra_query, extra_body=extra_body, timeout=timeout\n\u001b[32m   2070\u001b[39m         ),\n\u001b[32m   2071\u001b[39m         cast_to=ChatCompletion,\n\u001b[32m   2072\u001b[39m         stream=stream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m   2073\u001b[39m         stream_cls=AsyncStream[ChatCompletionChunk],\n\u001b[32m   2074\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/openai/_base_client.py:1742\u001b[39m, in \u001b[36mAsyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, files, options, stream, stream_cls)\u001b[39m\n\u001b[32m   1728\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1729\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1730\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1737\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_AsyncStreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1738\u001b[39m ) -> ResponseT | _AsyncStreamT:\n\u001b[32m   1739\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1740\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=\u001b[38;5;28;01mawait\u001b[39;00m async_to_httpx_files(files), **options\n\u001b[32m   1741\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1742\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m.request(cast_to, opts, stream=stream, stream_cls=stream_cls)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Live-Class/Agentic-1.0/Autogen/autogen-lc/lib/python3.12/site-packages/openai/_base_client.py:1549\u001b[39m, in \u001b[36mAsyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m   1546\u001b[39m             \u001b[38;5;28;01mawait\u001b[39;00m err.response.aread()\n\u001b[32m   1548\u001b[39m         log.debug(\u001b[33m\"\u001b[39m\u001b[33mRe-raising status error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1549\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._make_status_error_from_response(err.response) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1551\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[32m   1553\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m, \u001b[33m\"\u001b[39m\u001b[33mcould not resolve response (should never happen)\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[31mAuthenticationError\u001b[39m: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-proj-********************************************************************************************************************************************************qZkA. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}"]}], "source": ["result = await my_first_agent.run(task='Tell me a joke')\n", "print(result)"]}, {"cell_type": "markdown", "id": "7429f894", "metadata": {}, "source": ["messages=[TextMessage(source='user', models_usage=None, metadata={}, content='Tell me a joke', type='TextMessage'), TextMessage(source='My_First_Agent', models_usage=RequestUsage(prompt_tokens=40, completion_tokens=14), metadata={}, content=\"Why don't skeletons fight each other?\\n\\nThey don't have the guts.\", type='TextMessage')] stop_reason=None"]}, {"cell_type": "code", "execution_count": 16, "id": "2a473d62", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Why don't skeletons fight each other?\\n\\nThey don't have the guts.\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["result.messages[-1].content"]}, {"cell_type": "code", "execution_count": 5, "id": "a3126d33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"result\": 110\n", "}\n", "```\n"]}], "source": ["result = await my_first_agent.run(task='What is 5+105')\n", "print(result.messages[-1].content)"]}, {"cell_type": "code", "execution_count": null, "id": "c1380972", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "autogen-lc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}