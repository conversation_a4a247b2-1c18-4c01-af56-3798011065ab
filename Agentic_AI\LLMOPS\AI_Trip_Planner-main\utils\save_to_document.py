import os
import datetime

def save_document(response_text: str, directory: str = "./output"):
    """Export travel plan to Markdown file with proper formatting"""
    os.makedirs(directory, exist_ok=True)
    
    
    # Create markdown content with metadata header
    markdown_content = f"""# 🌍 AI Travel Plan

    # **Generated:** {datetime.datetime.now().strftime('%Y-%m-%d at %H:%M')}  
    # **Created by:** Atriyo's Travel Agent

    ---

    {response_text}

    ---

    *This travel plan was generated by AI. Please verify all information, especially prices, operating hours, and travel requirements before your trip.*
    """
            
    try:
        # Write to markdown file with UTF-8 encoding
        # Generate timestamp-based filename
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"{directory}/AI_Trip_Planner_{timestamp}.md"

        print(filename)

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"Markdown file saved as: {filename}")
        return filename
        
    except Exception as e:
        print(f"Error saving markdown file: {e}")
        return None