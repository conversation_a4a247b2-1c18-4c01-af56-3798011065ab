
def merge_sort(arr):
    if len(arr) > 1:
        mid = len(arr) // 2
        left_half = arr[:mid]
        right_half = arr[mid:]

        merge_sort(left_half)
        merge_sort(right_half)

        i = j = k = 0

        while i < len(left_half) and j < len(right_half):
            if left_half[i] < right_half[j]:
                arr[k] = left_half[i]
                i += 1
            else:
                arr[k] = right_half[j]
                j += 1
            k += 1

        while i < len(left_half):
            arr[k] = left_half[i]
            i += 1
            k += 1

        while j < len(right_half):
            arr[k] = right_half[j]
            j += 1
            k += 1

    return arr

# Test cases
test_case_1 = [38, 27, 43, 3, 9, 82, 10]
test_case_2 = [1, 60, -10, 70, -80, 85]
test_case_3 = [15, 25, 5, 30, 10]

sorted_1 = merge_sort(test_case_1.copy())
sorted_2 = merge_sort(test_case_2.copy())
sorted_3 = merge_sort(test_case_3.copy())

print("Sorted Array 1:", sorted_1)
print("Sorted Array 2:", sorted_2)
print("Sorted Array 3:", sorted_3)
