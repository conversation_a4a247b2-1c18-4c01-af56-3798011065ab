// DAX Measures converted from Tableau
// Generated by Tableau to Power BI Migration Engine
// Conversion Success Rate: 100.0%

// Profit Margin (measure)
// Original Tableau: SUM([Profit]) / SUM([SalesAmount])
[Profit Margin] = DIVIDE(SUM(Sales[Profit]), SUM(Sales[SalesAmount]), 0)

// Sales Category (dimension)
// Original Tableau: IF SUM([SalesAmount]) > 10000 THEN "High Sales" ELSEIF SUM([SalesAmount]) > 5000 THEN "Medium Sales" ELSE "Low Sales" END
[Sales Category] = IF(SUM(Sales[SalesAmount]) > 10000, "High Sales" ELSEIF SUM(Sales[SalesAmount]) > 5000 THEN "Medium Sales", "Low Sales")

// Year (dimension)
// Original Tableau: YEAR([OrderDate])
[Year] = YEAR(Sales[OrderDate])

// Quarter (dimension)
// Original Tableau: "Q" + STR(DATEPART("quarter", [OrderDate]))
[Quarter] = "Q" + FORMAT(FORMAT("quarter", Sales[OrderDate]))

// Region Filter (measure)
// Original Tableau: "All"
[Region Filter] = "All"

