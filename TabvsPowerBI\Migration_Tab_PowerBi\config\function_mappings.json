{"aggregation_functions": {"SUM": "SUM", "AVG": "AVERAGE", "AVERAGE": "AVERAGE", "COUNT": "COUNT", "COUNTD": "DISTINCTCOUNT", "MIN": "MIN", "MAX": "MAX", "MEDIAN": "MEDIAN", "STDEV": "STDEV.S", "STDEVP": "STDEV.P", "VAR": "VAR.S", "VARP": "VAR.P", "PERCENTILE": "PERCENTILE.INC"}, "string_functions": {"LEFT": "LEFT", "RIGHT": "RIGHT", "MID": "MID", "LEN": "LEN", "CONTAINS": "SEARCH", "STARTSWITH": "SEARCH", "ENDSWITH": "SEARCH", "UPPER": "UPPER", "LOWER": "LOWER", "TRIM": "TRIM", "LTRIM": "TRIM", "RTRIM": "TRIM", "REPLACE": "SUBSTITUTE", "SUBSTITUTE": "SUBSTITUTE", "FIND": "SEARCH", "SEARCH": "SEARCH", "SPLIT": "PATHITEM", "ASCII": "CODE", "CHAR": "UNICHAR"}, "date_functions": {"YEAR": "YEAR", "MONTH": "MONTH", "DAY": "DAY", "QUARTER": "FORMAT", "WEEK": "WEEKNUM", "WEEKDAY": "WEEKDAY", "HOUR": "HOUR", "MINUTE": "MINUTE", "SECOND": "SECOND", "NOW": "NOW", "TODAY": "TODAY", "DATE": "DATE", "DATETIME": "DATETIME", "TIME": "TIME", "DATEADD": "DATEADD", "DATEDIFF": "DATEDIFF", "DATEPART": "FORMAT", "DATETRUNC": "DATEADD", "DATENAME": "FORMAT", "ISDATE": "ISDATE"}, "logical_functions": {"IF": "IF", "IIF": "IF", "AND": "AND", "OR": "OR", "NOT": "NOT", "CASE": "SWITCH", "ISNULL": "ISBLANK", "IFNULL": "IF", "ZN": "IF", "ISNA": "ISERROR", "IFERROR": "IFERROR"}, "mathematical_functions": {"ABS": "ABS", "ROUND": "ROUND", "CEILING": "ROUNDUP", "FLOOR": "ROUNDDOWN", "SQRT": "SQRT", "POWER": "POWER", "EXP": "EXP", "LOG": "LOG", "LN": "LN", "SIN": "SIN", "COS": "COS", "TAN": "TAN", "ASIN": "ASIN", "ACOS": "ACOS", "ATAN": "ATAN", "ATAN2": "ATAN2", "PI": "PI", "RADIANS": "RADIANS", "DEGREES": "DEGREES", "SIGN": "SIGN", "MOD": "MOD", "DIV": "DIVIDE", "RANDOM": "RAND"}, "type_conversion_functions": {"STR": "FORMAT", "INT": "INT", "FLOAT": "VALUE", "BOOL": "VALUE", "DATE": "DATE", "DATETIME": "DATETIME"}, "window_functions": {"RANK": "RANKX", "RANK_DENSE": "RANKX", "RANK_MODIFIED": "RANKX", "RANK_PERCENTILE": "PERCENTRANK.INC", "RANK_UNIQUE": "RANKX", "ROW_NUMBER": "RANKX", "NTILE": "NTILE", "FIRST": "FIRSTNONBLANK", "LAST": "LASTNONBLANK", "PREVIOUS_VALUE": "EARLIER", "RUNNING_SUM": "CALCULATE", "RUNNING_AVG": "CALCULATE", "RUNNING_COUNT": "CALCULATE", "RUNNING_MIN": "CALCULATE", "RUNNING_MAX": "CALCULATE", "WINDOW_SUM": "CALCULATE", "WINDOW_AVG": "CALCULATE", "WINDOW_COUNT": "CALCULATE", "WINDOW_MIN": "CALCULATE", "WINDOW_MAX": "CALCULATE", "TOTAL": "CALCULATE", "LOOKUP": "LOOKUPVALUE"}, "lod_functions": {"FIXED": "CALCULATE", "INCLUDE": "CALCULATE", "EXCLUDE": "CALCULATE"}, "special_tableau_functions": {"ATTR": "VALUES", "SIZE": "COUNTROWS", "INDEX": "RANKX", "MAKEDATE": "DATE", "MAKEDATETIME": "DATETIME", "MAKETIME": "TIME", "HEXBINX": "CUSTOM", "HEXBINY": "CUSTOM", "DISTANCE": "CUSTOM", "BUFFER": "CUSTOM"}, "conversion_patterns": {"tableau_if_pattern": "IF {condition} THEN {true_value} ELSE {false_value} END", "dax_if_pattern": "IF({condition}, {true_value}, {false_value})", "tableau_case_pattern": "CASE {field} WHEN {value1} THEN {result1} WHEN {value2} THEN {result2} ELSE {default} END", "dax_case_pattern": "SWITCH({field}, {value1}, {result1}, {value2}, {result2}, {default})", "tableau_lod_fixed": "{FIXED {dimensions} : {expression}}", "dax_lod_fixed": "CALCULATE({expression}, ALLEXCEPT(Table, {dimensions}))", "tableau_lod_include": "{INCLUDE {dimensions} : {expression}}", "dax_lod_include": "CALCULATE({expression}, VALUES(Table[{dimensions}]))", "tableau_lod_exclude": "{EXCLUDE {dimensions} : {expression}}", "dax_lod_exclude": "CALCULATE({expression}, ALL(Table[{dimensions}]))"}, "complex_conversions": {"tableau_running_sum": "RUNNING_SUM(SUM([Field]))", "dax_running_sum": "CALCULATE(SUM(Table[Field]), FILTER(ALL(Table), Table[Date] <= EARLIER(Table[Date])))", "tableau_rank": "RANK(SUM([Field]))", "dax_rank": "RANKX(ALL(Table), SUM(Table[Field]))", "tableau_percentile": "PERCENTILE([Field], 0.5)", "dax_percentile": "PERCENTILE.INC(Table[Field], 0.5)", "tableau_previous_value": "PREVIOUS_VALUE(0)", "dax_previous_value": "CALCULATE(SUM(Table[Field]), FILTER(ALL(Table), Table[Date] = EARLIER(Table[Date]) - 1))"}}