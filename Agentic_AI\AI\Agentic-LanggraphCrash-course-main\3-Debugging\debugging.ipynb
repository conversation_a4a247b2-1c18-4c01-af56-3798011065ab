{"cells": [{"cell_type": "code", "execution_count": 5, "id": "041960c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Annotated\n", "from typing_extensions import TypedDict\n", "from langgraph.graph import END, START\n", "from langgraph.graph.state import StateGraph\n", "from langgraph.graph.message import add_messages\n", "from langgraph.prebuilt import ToolNode\n", "from langchain_core.tools import tool\n", "from langchain_core.messages import BaseMessage\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 31, "id": "2922bed9", "metadata": {}, "outputs": [], "source": ["os.environ[\"LANGSMITH_PROJECT\"]=\"TestProject\""]}, {"cell_type": "code", "execution_count": 2, "id": "20277adb", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"GROQ_API_KEY\"]=os.getenv(\"GROQ_API_KEY\")\n", "os.environ[\"LANGSMITH_API_KEY\"]=os.getenv(\"LANGCHAIN_API_KEY\")\n", "os.environ[\"LANGSMITH_TRACING\"]=\"true\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "bbddb96f", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x000001818A5238C0>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x000001818A6C4590>, model_name='llama3-8b-8192', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chat_models import init_chat_model\n", "llm=init_chat_model(\"groq:llama3-8b-8192\")\n", "llm"]}, {"cell_type": "code", "execution_count": 6, "id": "3916246c", "metadata": {}, "outputs": [], "source": ["class State(TypedDict):\n", "    messages:Annotated[list[BaseMessage],add_messages]"]}, {"cell_type": "code", "execution_count": 32, "id": "a3965619", "metadata": {}, "outputs": [], "source": ["## Graph With tool Call\n", "from langchain_core.tools import tool\n", "\n", "@tool\n", "def add(a:float,b:float):\n", "    \"\"\"Add two number\"\"\"\n", "    return a+b\n", "tools=[add]\n", "tool_node=ToolNode([add])\n", "\n", "llm_with_tool=llm.bind_tools([add])\n", "\n", "def call_llm_model(state:State):\n", "    return {\"messages\":[llm_with_tool.invoke(state['messages'])]}\n"]}, {"cell_type": "code", "execution_count": 33, "id": "5e5362d9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## Stategraph\n", "from langgraph.graph import StateGraph,START,END\n", "from langgraph.prebuilt import ToolNode\n", "from langgraph.prebuilt import tools_condition\n", "\n", "## Node definition\n", "def call_llm_model(state:State):\n", "    return {\"messages\":[llm_with_tool.invoke(state['messages'])]}\n", "## Grpah\n", "builder=StateGraph(State)\n", "builder.add_node(\"tool_calling_llm\",call_llm_model)\n", "builder.add_node(\"tools\",ToolNode(tools))\n", "\n", "## Add Edges\n", "builder.add_edge(START, \"tool_calling_llm\")\n", "builder.add_conditional_edges(\n", "    \"tool_calling_llm\",\n", "    # If the latest message (result) from assistant is a tool call -> tools_condition routes to tools\n", "    # If the latest message (result) from assistant is a not a tool call -> tools_condition routes to END\n", "    tools_condition\n", ")\n", "builder.add_edge(\"tools\",\"tool_calling_llm\")\n", "\n", "## compile the graph\n", "graph=builder.compile()\n", "\n", "from IPython.display import Image, display\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 35, "id": "8c05fb34", "metadata": {}, "outputs": [], "source": ["response=graph.invoke({\"messages\":\"What is machine learning\"})"]}, {"cell_type": "code", "execution_count": 36, "id": "d6011c15", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='What is machine learning', additional_kwargs={}, response_metadata={}, id='7572b892-93e5-463e-bee5-416017c30bb6'),\n", "  AIMessage(content='Machine learning is a type of artificial intelligence (AI) that enables computers to learn from data without being explicitly programmed. It involves training algorithms on large datasets to identify patterns, make predictions, and improve their performance over time.\\n\\nMachine learning is a subfield of artificial intelligence that focuses on developing algorithms and statistical models that enable machines to learn from data and make decisions. The goal of machine learning is to enable machines to automatically improve their performance on a task without being explicitly programmed.\\n\\nMachine learning can be used for a wide range of applications, including image and speech recognition, natural language processing, recommender systems, and autonomous vehicles. It is also used in many industries, such as healthcare, finance, and marketing, to improve decision-making and automate processes.\\n\\nThere are many types of machine learning, including:\\n\\n1. Supervised learning: This type of machine learning involves training algorithms on labeled data, where the output is already known. The algorithm learns to map inputs to outputs based on the labeled data and can then make predictions on new, unseen data.\\n2. Unsupervised learning: This type of machine learning involves training algorithms on unlabeled data, where the output is not known. The algorithm learns to identify patterns and relationships in the data and can then group similar data points together or identify outliers.\\n3. Reinforcement learning: This type of machine learning involves training algorithms to make decisions based on rewards or penalties. The algorithm learns to select actions that maximize a reward signal and can then make decisions in complex, dynamic environments.\\n4. Deep learning: This type of machine learning involves training neural networks on large datasets to learn complex patterns and relationships. Deep learning is often used for image and speech recognition, natural language processing, and other tasks that require processing large amounts of data.', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 354, 'prompt_tokens': 1811, 'total_tokens': 2165, 'completion_time': 0.749905812, 'prompt_time': 0.297216316, 'queue_time': -0.44429328599999995, 'total_time': 1.047122128}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--b8d65d5c-ee57-4567-83f1-651ba5fd3767-0', usage_metadata={'input_tokens': 1811, 'output_tokens': 354, 'total_tokens': 2165})]}"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 37, "id": "523ceb1b", "metadata": {}, "outputs": [], "source": ["response=graph.invoke({\"messages\":\"what is 5 plus 20\"})"]}, {"cell_type": "code", "execution_count": 30, "id": "4407c973", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='what is 5 plus 20', additional_kwargs={}, response_metadata={}, id='c50b70a3-e924-4f68-a13c-f3739e632b7e'),\n", "  AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'nc8a5spm9', 'function': {'arguments': '{\"a\":5,\"b\":20}', 'name': 'add'}, 'type': 'function'}]}, response_metadata={'token_usage': {'completion_tokens': 72, 'prompt_tokens': 898, 'total_tokens': 970, 'completion_time': 0.144936166, 'prompt_time': 0.368290252, 'queue_time': 0.483140518, 'total_time': 0.513226418}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8af39bf2ae', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run--fe10d726-9c8a-4da7-a787-7415e932ef55-0', tool_calls=[{'name': 'add', 'args': {'a': 5, 'b': 20}, 'id': 'nc8a5spm9', 'type': 'tool_call'}], usage_metadata={'input_tokens': 898, 'output_tokens': 72, 'total_tokens': 970}),\n", "  ToolMessage(content='25.0', name='add', id='8f478294-cabc-4c7d-aa61-51e493966247', tool_call_id='nc8a5spm9'),\n", "  AIMessage(content='25.0', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 4, 'prompt_tokens': 1961, 'total_tokens': 1965, 'completion_time': 0.009678836, 'prompt_time': 0.218778985, 'queue_time': -0.296110054, 'total_time': 0.228457821}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8b7c3a83f7', 'finish_reason': 'stop', 'logprobs': None}, id='run--a29cbdf6-68d0-476b-ae31-c27e6f9fb28b-0', usage_metadata={'input_tokens': 1961, 'output_tokens': 4, 'total_tokens': 1965})]}"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "id": "74560b24", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "AgenticLanggraph", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}