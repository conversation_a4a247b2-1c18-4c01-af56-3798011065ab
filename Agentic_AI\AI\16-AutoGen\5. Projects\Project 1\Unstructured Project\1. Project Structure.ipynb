{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Professional Project Structure for AutoGen Applications\n", "\n", "## Learning Objectives\n", "- Understand why project structure matters in Agentic AI\n", "- Learn industry-standard organization patterns\n", "- Implement a maintainable AutoGen project architecture\n", "- Apply these principles to real-world use cases"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Foundations of Project Structure\n", "\n", "### 1.1 Why Organization Matters\n", "**Key Problems Poor Structure Creates:**\n", "- 🌀 Spaghetti code in multi-agent systems\n", "- 🔍 Difficulty finding components\n", "- 🤝 Collaboration bottlenecks\n", "- 🚀 Scaling limitations\n", "\n", "**Benefits of Good Structure:**\n", "- 🧩 Clear component boundaries\n", "- 📁 Intuitive file locations\n", "- 🔄 Easy updates/maintenance\n", "- 🧪 Simplified testing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Core Architectural Principles\n", "\n", "```text\n", "MODULARITY\n", "└── Separation of Concerns\n", "    ├── Agents → Individual capabilities\n", "    ├── Teams → Collaboration workflows\n", "    ├── Config → Settings/constants\n", "    └── Utils → Shared functionality\n", "\n", "SCALABILITY\n", "└── Design for Growth\n", "    ├── Add agents without breaking changes\n", "    ├── Extend teams with new workflows\n", "    └── Support new integrations\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. AutoGen Project Blueprint\n", "\n", "### 2.1 Directory Structure\n", "```text\n", "autogen_project/\n", "├── 📂 config/               # Configuration\n", "│   └── settings.py         # API keys, model params\n", "├── 📂 agents/               # Agent definitions\n", "│   ├── planner.py          # Role-specific agents\n", "│   └── researcher.py\n", "├── 📂 teams/                # Collaboration\n", "│   └── travel_team.py      # GroupChat configurations\n", "├── 📂 utils/                # Shared resources\n", "│   ├── logging.py          # Monitoring\n", "│   └── state.py            # Conversation persistence\n", "├── 📜 main.py               # Entry point\n", "└── 📜 requirements.txt      # Dependencies\n", "```\n", "We can also have a models folder with all the models defined.\n", "\n", "### 2.2 File Responsibilities\n", "| File/Folder       | Purpose                                                                 |\n", "|-------------------|-------------------------------------------------------------------------|\n", "| `config/`         | Centralize all configuration (API keys, model names, limits)          |\n", "| `agents/`         | Isolate individual agent definitions and behaviors                     |\n", "| `teams/`          | Define how agents interact (GroupChat, conversation patterns)         |\n", "| `utils/`          | Reusable functions (logging, state management, helper tools)          |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Implementation Guide\n", "\n", "### 3.1 Configuration Management\n", "**Best Practices:**\n", "- Never hardcode sensitive values\n", "- Use environment variables\n", "- Type-hint important settings\n", "\n", "Example (`config/settings.py`):\n", "```python\n", "# Environment variables\n", "API_KEY = os.getenv('OPENAI_API_KEY')\n", "\n", "# Model configuration\n", "MODEL = 'gpt-4'\n", "TEMPERATURE = 0.7\n", "\n", "# Conversation limits\n", "MAX_TURNS = 15\n", "TERMINATION_MSG = 'TERMINATE'\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Agent Definition Patterns\n", "\n", "**Standard Approach (`agents/planner.py`):**\n", "```python\n", "from autogen_agentchat.agents import AssistantAgent\n", "from config.settings import MODEL\n", "\n", "def create_planner():\n", "    return AssistantAgent(\n", "        name=\"Travel_Planner\",\n", "        system_message=\"\"\"You specialize in...\"\"\",\n", "        llm_config={\"model\": MODEL}\n", "    )\n", "```\n", "\n", "**Key Benefits:**\n", "- Self-contained agent definitions\n", "- Easy to modify individual agents\n", "- Clear separation from team logic"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Team Composition\n", "\n", "**Workflow Design (`teams/travel_team.py`):**\n", "```python\n", "\n", "def build_team():\n", "    agents = [create_planner(), create_researcher()]\n", "    \n", "    return GroupChat(\n", "        agents=agents,\n", "        messages=[],\n", "        max_round=10\n", "    )\n", "```\n", "\n", "**Team Design Principles:**\n", "- Compose from individual agents\n", "- Centralize conversation rules\n", "- Isolate workflow configuration"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Advanced Organization\n", "\n", "### 4.1 Testing Strategy\n", "```text\n", "tests/\n", "├── unit/\n", "│   ├── test_agents.py    # Individual agent tests\n", "│   └── test_utils.py     # Utility function tests\n", "└── integration/\n", "    └── test_teams.py     # Team workflow tests\n", "```\n", "\n", "**Example Test Case:**\n", "```python\n", "def test_planner_response():\n", "    planner = create_planner()\n", "    response = planner.generate_reply([{\"content\": \"...\"}])\n", "    assert \"itinerary\" in response.lower()\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Production Readiness\n", "\n", "**Essential Additions:**\n", "```text\n", "production_project/\n", "├── 📂 monitoring/          # Observability\n", "│   ├── metrics.py         # Performance tracking\n", "│   └── alerts.py          # Notification system\n", "├── 📜 Dockerfile           # Containerization\n", "└── 📜 .env.sample          # Configuration template\n", "```\n", "\n", "**Monitoring Example:**\n", "```python\n", "# utils/monitoring.py\n", "from prometheus_client import Counter\n", "\n", "AGENT_ERRORS = Counter(\n", "    'agent_errors',\n", "    'Count of agent failures',\n", "    ['agent_name']\n", ")\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Framework Comparison\n", "\n", "### 5.1 Structure Adaptation Guide\n", "\n", "| Concept        | AutoGen        | CrewAI         | LangChain      |\n", "|----------------|----------------|----------------|----------------|\n", "| Agents         | `agents/`      | `agents/`      | `agents/`      |\n", "| Teams          | `teams/`       | `crews/`       | `chains/`      |\n", "| Tools          | `tools/`       | `tools/`       | `tools/`       |\n", "| Config         | `config/`      | `config/`      | `config/`      |\n", "\n", "**Key Insight:**\n", "- Core concepts map across frameworks\n", "- Only team/chaining logic differs\n", "- Shared utilities remain consistent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Practical Implementation\n", "\n", "### 6.1 Travel Planner Example\n", "\n", "**File Structure:**\n", "```text\n", "travel_ai/\n", "├── agents/\n", "│   ├── destination_expert.py\n", "│   ├── budget_advisor.py\n", "│   └── itinerary_writer.py\n", "└── teams/\n", "    └── vacation_planner.py\n", "```\n", "\n", "**Workflow Process:**\n", "1. User request → `main.py`\n", "2. Initializes team from `teams/vacation_planner.py`\n", "3. Agents collaborate through GroupChat\n", "4. Final itinerary returned to user"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Maintenance Checklist\n", "\n", "**Project Health Metrics:**\n", "- [ ] Modular components (no cross-file dependencies)\n", "- [ ] Complete test coverage\n", "- [ ] Up-to-date documentation\n", "- [ ] CI/CD pipeline\n", "- [ ] Monitoring dashboard\n", "- [ ] Security review\n", "\n", "**Evolution Tips:**\n", "- Start simple (single agent)\n", "- Add complexity incrementally\n", "- Refactor when adding 3rd+ agent\n", "- Document architectural decisions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "**Remember:** Good structure pays dividends as projects grow!"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}