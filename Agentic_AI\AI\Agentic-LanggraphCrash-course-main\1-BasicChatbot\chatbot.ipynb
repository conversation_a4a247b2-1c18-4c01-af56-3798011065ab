{"cells": [{"cell_type": "markdown", "id": "7e06ee86", "metadata": {}, "source": ["### Build A Basic Chatbot With Langgraph(GRAPH API)"]}, {"cell_type": "code", "execution_count": 1, "id": "9055239a", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph,START,END\n", "from langgraph.graph.message import add_messages\n"]}, {"cell_type": "code", "execution_count": 2, "id": "b393f690", "metadata": {}, "outputs": [], "source": ["class State(TypedDict):\n", "    # Messages have the type \"list\". The `add_messages` function\n", "    # in the annotation defines how this state key should be updated\n", "    # (in this case, it appends messages to the list, rather than overwriting them)\n", "    messages:Annotated[list,add_messages]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f7d367fa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "d23391db", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 4, "id": "0d18f35a", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq\n", "from langchain.chat_models import init_chat_model\n", "\n", "llm=ChatGroq(model=\"llama3-8b-8192\")"]}, {"cell_type": "code", "execution_count": 7, "id": "b00e0b09", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x00000222B433E120>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x00000222B433ECF0>, model_name='llama3-8b-8192', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["llm"]}, {"cell_type": "code", "execution_count": 5, "id": "dc2c52ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x00000169B9EC8F50>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x00000169B9EC9950>, model_name='llama3-8b-8192', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["llm=init_chat_model(\"groq:llama3-8b-8192\")\n", "llm"]}, {"cell_type": "code", "execution_count": 9, "id": "af50c0a6", "metadata": {}, "outputs": [], "source": ["## Node Functionality\n", "def chatbot(state:State):\n", "    return {\"messages\":[llm.invoke(state[\"messages\"])]}"]}, {"cell_type": "code", "execution_count": 10, "id": "11b77875", "metadata": {}, "outputs": [], "source": ["graph_builder=StateGraph(State)\n", "\n", "## Adding node\n", "graph_builder.add_node(\"llmchatbot\",chatbot)\n", "## Adding Edges\n", "graph_builder.add_edge(START,\"llmchatbot\")\n", "graph_builder.add_edge(\"llm<PERSON>t<PERSON>\",END)\n", "\n", "## compile the graph\n", "graph=graph_builder.compile()"]}, {"cell_type": "code", "execution_count": 11, "id": "17b74dd8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## Visualize the graph\n", "from IPython.display import Image,display\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    pass"]}, {"cell_type": "code", "execution_count": 13, "id": "1d91a7e9", "metadata": {}, "outputs": [], "source": ["response=graph.invoke({\"messages\":\"Hi\"})"]}, {"cell_type": "code", "execution_count": 16, "id": "323b71a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Hi! It's nice to meet you. Is there something I can help you with or would you like to chat?\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["response[\"messages\"][-1].content"]}, {"cell_type": "code", "execution_count": 19, "id": "bd9240e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hi! I'm just a language model, so I don't have emotions or feelings like humans do, but I'm functioning properly and ready to help you with any questions or tasks you may have! How can I assist you today?\n"]}], "source": ["for event in graph.stream({\"messages\":\"Hi How are you?\"}):\n", "    for value in event.values():\n", "        print(value[\"messages\"][-1].content)"]}, {"cell_type": "markdown", "id": "a04b696d", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> With Tool"]}, {"cell_type": "code", "execution_count": 5, "id": "447e146a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'query': 'What is langgraph',\n", " 'follow_up_questions': None,\n", " 'answer': None,\n", " 'images': [],\n", " 'results': [{'title': 'LangGraph Tutorial: What Is LangGraph and How to Use It?',\n", "   'url': 'https://www.datacamp.com/tutorial/langgraph-tutorial',\n", "   'content': 'LangGraph is a library within the LangChain ecosystem that provides a framework for defining, coordinating, and executing multiple LLM agents (or chains) in a structured and efficient manner. By managing the flow of data and the sequence of operations, LangGraph allows developers to focus on the high-level logic of their applications rather than the intricacies of agent coordination. Whether you need a chatbot that can handle various types of user requests or a multi-agent system that performs complex tasks, LangGraph provides the tools to build exactly what you need. LangGraph significantly simplifies the development of complex LLM applications by providing a structured framework for managing state and coordinating agent interactions.',\n", "   'score': 0.9502313,\n", "   'raw_content': None},\n", "  {'title': 'What is <PERSON><PERSON><PERSON><PERSON>? - GeeksforGeeks',\n", "   'url': 'https://www.geeksforgeeks.org/machine-learning/what-is-langgraph/',\n", "   'content': 'LangGraph is a Python library that helps you build applications like chatbots or AI agents by organizing their logic step-by-step using state machine model. This step configures your Gemini API key and then we create a simple function `ask_gemini` that takes user input, sends it to the Gemini model and returns the AI-generated response. Creates a state structure with three fields: `question`, `classification` and `response` which flows through the LangGraph. import matplotlib.pyplot as plt from langgraph.graph import StateGraph\\u200bbuilder = StateGraph(GraphState)builder.add_node(\"classify\", classify)builder.add_node(\"respond\", respond)builder.set_entry_point(\"classify\")builder.add_edge(\"classify\", \"respond\")builder.set_finish_point(\"respond\")app = builder.compile()\\u200bdef visualize_workflow(builder): G = nx.DiGraph()\\u200b for node in builder.nodes: G.add_node(node) for edge in builder.edges: G.add_edge(edge[0], edge[1])\\u200b pos = nx.spring_layout(G) nx.draw(G, pos, with_labels=True, node_size=3000, node_color=\"skyblue\", font_size=12, font_weight=\"bold\", arrows=True)  plt.title(\"Langchain Workflow Visualization\") plt.show()\\u200bvisualize_workflow(builder)',\n", "   'score': 0.90849644,\n", "   'raw_content': None}],\n", " 'response_time': 1.66}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_tavily import TavilySearch\n", "\n", "tool=TavilySearch(max_results=2)\n", "tool.invoke(\"What is langgraph\")"]}, {"cell_type": "code", "execution_count": 6, "id": "cd185da9", "metadata": {}, "outputs": [], "source": ["## Custom function\n", "def multiply(a:int,b:int)->int:\n", "    \"\"\"Multiply a and b\n", "\n", "    Args:\n", "        a (int): first int\n", "        b (int): second int\n", "\n", "    Returns:\n", "        int: output int\n", "    \"\"\"\n", "    return a*b"]}, {"cell_type": "code", "execution_count": 7, "id": "bf5e176d", "metadata": {}, "outputs": [], "source": ["tools=[tool,multiply]"]}, {"cell_type": "code", "execution_count": 8, "id": "046be79e", "metadata": {}, "outputs": [], "source": ["llm_with_tool=llm.bind_tools(tools)"]}, {"cell_type": "code", "execution_count": 9, "id": "e19544d2", "metadata": {}, "outputs": [{"data": {"text/plain": ["RunnableBinding(bound=ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x00000221887816A0>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x0000022188782270>, model_name='llama3-8b-8192', model_kwargs={}, groq_api_key=SecretStr('**********')), kwargs={'tools': [{'type': 'function', 'function': {'name': 'tavily_search', 'description': 'A search engine optimized for comprehensive, accurate, and trusted results. Useful for when you need to answer questions about current events. It not only retrieves URLs and snippets, but offers advanced search depths, domain management, time range filters, and image search, this tool delivers real-time, accurate, and citation-backed results.Input should be a search query.', 'parameters': {'properties': {'query': {'description': 'Search query to look up', 'type': 'string'}, 'include_domains': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': [], 'description': 'A list of domains to restrict search results to.\\n\\n        Use this parameter when:\\n        1. The user explicitly requests information from specific websites (e.g., \"Find climate data from nasa.gov\")\\n        2. The user mentions an organization or company without specifying the domain (e.g., \"Find information about iPhones from Apple\")\\n\\n        In both cases, you should determine the appropriate domains (e.g., [\"nasa.gov\"] or [\"apple.com\"]) and set this parameter.\\n\\n        Results will ONLY come from the specified domains - no other sources will be included.\\n        Default is None (no domain restriction).\\n        '}, 'exclude_domains': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': [], 'description': 'A list of domains to exclude from search results.\\n\\n        Use this parameter when:\\n        1. The user explicitly requests to avoid certain websites (e.g., \"Find information about climate change but not from twitter.com\")\\n        2. The user mentions not wanting results from specific organizations without naming the domain (e.g., \"Find phone reviews but nothing from Apple\")\\n\\n        In both cases, you should determine the appropriate domains to exclude (e.g., [\"twitter.com\"] or [\"apple.com\"]) and set this parameter.\\n\\n        Results will filter out all content from the specified domains.\\n        Default is None (no domain exclusion).\\n        '}, 'search_depth': {'anyOf': [{'enum': ['basic', 'advanced'], 'type': 'string'}, {'type': 'null'}], 'default': 'basic', 'description': 'Controls search thoroughness and result comprehensiveness.\\n    \\n        Use \"basic\" for simple queries requiring quick, straightforward answers.\\n        \\n        Use \"advanced\" (default) for complex queries, specialized topics, \\n        rare information, or when in-depth analysis is needed.\\n        '}, 'include_images': {'anyOf': [{'type': 'boolean'}, {'type': 'null'}], 'default': False, 'description': 'Determines if the search returns relevant images along with text results.\\n   \\n        Set to True when the user explicitly requests visuals or when images would \\n        significantly enhance understanding (e.g., \"Show me what black holes look like,\" \\n        \"Find pictures of Renaissance art\").\\n        \\n        Leave as False (default) for most informational queries where text is sufficient.\\n        '}, 'time_range': {'anyOf': [{'enum': ['day', 'week', 'month', 'year'], 'type': 'string'}, {'type': 'null'}], 'default': None, 'description': 'Limits results to content published within a specific timeframe.\\n        \\n        ONLY set this when the user explicitly mentions a time period \\n        (e.g., \"latest AI news,\" \"articles from last week\").\\n        \\n        For less popular or niche topics, use broader time ranges \\n        (\"month\" or \"year\") to ensure sufficient relevant results.\\n   \\n        Options: \"day\" (24h), \"week\" (7d), \"month\" (30d), \"year\" (365d).\\n        \\n        Default is None.\\n        '}, 'topic': {'anyOf': [{'enum': ['general', 'news', 'finance'], 'type': 'string'}, {'type': 'null'}], 'default': 'general', 'description': 'Specifies search category for optimized results.\\n   \\n        Use \"general\" (default) for most queries, INCLUDING those with terms like \\n        \"latest,\" \"newest,\" or \"recent\" when referring to general information.\\n\\n        Use \"finance\" for markets, investments, economic data, or financial news.\\n\\n        Use \"news\" ONLY for politics, sports, or major current events covered by \\n        mainstream media - NOT simply because a query asks for \"new\" information.\\n        '}}, 'required': ['query'], 'type': 'object'}}}, {'type': 'function', 'function': {'name': 'multiply', 'description': 'Multiply a and b', 'parameters': {'properties': {'a': {'description': 'first int', 'type': 'integer'}, 'b': {'description': 'second int', 'type': 'integer'}}, 'required': ['a', 'b'], 'type': 'object'}}}]}, config={}, config_factories=[])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_with_tool"]}, {"cell_type": "code", "execution_count": 11, "id": "550d837b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## Stategraph\n", "from langgraph.graph import StateGraph,START,END\n", "from langgraph.prebuilt import ToolNode\n", "from langgraph.prebuilt import tools_condition\n", "\n", "## Node definition\n", "def tool_calling_llm(state:State):\n", "    return {\"messages\":[llm_with_tool.invoke(state[\"messages\"])]}\n", "\n", "## Grpah\n", "builder=StateGraph(State)\n", "builder.add_node(\"tool_calling_llm\",tool_calling_llm)\n", "builder.add_node(\"tools\",ToolNode(tools))\n", "\n", "## Add Edges\n", "builder.add_edge(START, \"tool_calling_llm\")\n", "builder.add_conditional_edges(\n", "    \"tool_calling_llm\",\n", "    # If the latest message (result) from assistant is a tool call -> tools_condition routes to tools\n", "    # If the latest message (result) from assistant is a not a tool call -> tools_condition routes to END\n", "    tools_condition\n", ")\n", "builder.add_edge(\"tools\",END)\n", "\n", "## compile the graph\n", "graph=builder.compile()\n", "\n", "from IPython.display import Image, display\n", "display(Image(graph.get_graph().draw_mermaid_png()))\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "id": "d802d254", "metadata": {}, "outputs": [], "source": ["response=graph.invoke({\"messages\":\"What is the recent ai news\"})"]}, {"cell_type": "code", "execution_count": 13, "id": "0f6168c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"query\": \"recent ai news\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://www.bbc.com/news/articles/cn0q2v851k9o\", \"title\": \"Amazon boss says AI will replace jobs at tech giant - BBC\", \"score\": 0.71995544, \"published_date\": \"Tu<PERSON>, 17 Jun 2025 21:31:36 GMT\", \"content\": \"Companies, especially in the tech sector, have been investing heavily in AI in recent years, spurred on by technological advances that have made it easier than ever for chatbots to create code, images and text with limited instruction.\\\\n\\\\nBut as the new tools gain traction, they have sparked warnings from some tech leaders of job losses, especially in entry-level office roles. [...] <PERSON><PERSON>, chief executive of AI-firm Anthropic, told news website Axios last month that the technology could wipe out half of entry-level white collar jobs.\\\\n\\\\n<PERSON><PERSON><PERSON><PERSON>, whose work on AI, including at Google, has earned him the moniker \\\\\"Godfather of AI\\\\\", echoed those warnings on a recent podcast.\", \"raw_content\": null}, {\"url\": \"https://www.csoonline.com/article/4008912/wormgpt-returns-new-malicious-ai-variants-built-on-grok-and-mixtral-uncovered.html\", \"title\": \"WormGPT returns: New malicious AI variants built on Grok and Mixtral uncovered - csoonline.com\", \"score\": 0.70214266, \"published_date\": \"Wed, 18 Jun 2025 12:17:59 GMT\", \"content\": \"news ### New npm threats can erase production systems with a single request Jun 10, 2025 3 mins\\\\n   news ### Chrome extension privacy promises undone by hardcoded secrets, leaky HTTP Jun 9, 2025 4 mins\\\\n   news ### New phishing campaign hijacks clipboard via fake CAPTCHA for malware delivery Jun 6, 2025 4 mins\\\\n   news ### Supply chain attack hits RubyGems to steal Telegram API data Jun 5, 2025 4 mins [...] Related content\\\\n---------------\\\\n\\\\nOpinion ### Security, risk and compliance in the world of AI agents By Nikhil Sarnot Jun 17, 2025 12 mins Artificial Intelligence Compliance IT GovernanceNews ### Phishing goes prime time: Hackers use trusted sites to hijack search rankings By Shweta Sharma Jun 17, 2025 5 mins Phishing SecurityNews ### Malicious PyPI package targets Chimera users to steal AWS tokens, CI/CD secrets By Shweta Sharma Jun 17, 2025 4 mins Developer Malware Security [...] news ### ‘Grafana Ghost’ XSS flaw exposes 47,000 servers to account takeover Jun 16, 2025 3 mins\\\\n   news ### Fog ransomware gang abuses employee monitoring tool in unusual multi-stage attack Jun 13, 2025 4 mins\\\\n   news ### Phishing sites posing as DeepSeek downloads drop a proxy backdoor Jun 12, 2025 4 mins\\\\n   news ### China-linked hackers target cybersecurity firms, governments in global espionage campaign Jun 11, 2025 4 mins\", \"raw_content\": null}], \"response_time\": 2.3}'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["response['messages'][-1].content"]}, {"cell_type": "code", "execution_count": 14, "id": "8051ed54", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is the recent ai news\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  tavily_search (9hkqr0mx3)\n", " Call ID: 9hkqr0mx3\n", "  Args:\n", "    exclude_domains: []\n", "    include_domains: []\n", "    include_images: False\n", "    query: recent ai news\n", "    search_depth: advanced\n", "    time_range: day\n", "    topic: news\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"recent ai news\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://www.bbc.com/news/articles/cn0q2v851k9o\", \"title\": \"Amazon boss says AI will replace jobs at tech giant - BBC\", \"score\": 0.71995544, \"published_date\": \"<PERSON><PERSON>, 17 Jun 2025 21:31:36 GMT\", \"content\": \"Companies, especially in the tech sector, have been investing heavily in AI in recent years, spurred on by technological advances that have made it easier than ever for chatbots to create code, images and text with limited instruction.\\n\\nBut as the new tools gain traction, they have sparked warnings from some tech leaders of job losses, especially in entry-level office roles. [...] <PERSON><PERSON>, chief executive of AI-firm Anthropic, told news website Axios last month that the technology could wipe out half of entry-level white collar jobs.\\n\\n<PERSON><PERSON><PERSON><PERSON>, whose work on AI, including at Google, has earned him the moniker \\\"Godfather of AI\\\", echoed those warnings on a recent podcast.\", \"raw_content\": null}, {\"url\": \"https://www.csoonline.com/article/4008912/wormgpt-returns-new-malicious-ai-variants-built-on-grok-and-mixtral-uncovered.html\", \"title\": \"WormGPT returns: New malicious AI variants built on Grok and Mixtral uncovered - csoonline.com\", \"score\": 0.70214266, \"published_date\": \"Wed, 18 Jun 2025 12:17:59 GMT\", \"content\": \"news ### New npm threats can erase production systems with a single request Jun 10, 2025 3 mins\\n   news ### Chrome extension privacy promises undone by hardcoded secrets, leaky HTTP Jun 9, 2025 4 mins\\n   news ### New phishing campaign hijacks clipboard via fake CAPTCHA for malware delivery Jun 6, 2025 4 mins\\n   news ### Supply chain attack hits RubyGems to steal Telegram API data Jun 5, 2025 4 mins [...] Related content\\n---------------\\n\\nOpinion ### Security, risk and compliance in the world of AI agents By Nikhil Sarnot Jun 17, 2025 12 mins Artificial Intelligence Compliance IT GovernanceNews ### Phishing goes prime time: Hackers use trusted sites to hijack search rankings By Shweta Sharma Jun 17, 2025 5 mins Phishing SecurityNews ### Malicious PyPI package targets Chimera users to steal AWS tokens, CI/CD secrets By Shweta Sharma Jun 17, 2025 4 mins Developer Malware Security [...] news ### ‘Grafana Ghost’ XSS flaw exposes 47,000 servers to account takeover Jun 16, 2025 3 mins\\n   news ### Fog ransomware gang abuses employee monitoring tool in unusual multi-stage attack Jun 13, 2025 4 mins\\n   news ### Phishing sites posing as DeepSeek downloads drop a proxy backdoor Jun 12, 2025 4 mins\\n   news ### China-linked hackers target cybersecurity firms, governments in global espionage campaign Jun 11, 2025 4 mins\", \"raw_content\": null}], \"response_time\": 2.3}\n"]}], "source": ["for m in response['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": 15, "id": "36f8fb4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is 5 multiplied by 2\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  multiply (g6rrcbzyx)\n", " Call ID: g6rrcbzyx\n", "  Args:\n", "    a: 5\n", "    b: 2\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: multiply\n", "\n", "10\n"]}], "source": ["response=graph.invoke({\"messages\":\"What is 5 multiplied by 2\"})\n", "for m in response['messages']:\n", "    m.pretty_print()\n"]}, {"cell_type": "code", "execution_count": 16, "id": "adb7c3fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Give me the recent ai news and then multiply 5 by 10\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  tavily_search (y5rf0ar1k)\n", " Call ID: y5rf0ar1k\n", "  Args:\n", "    query: recent ai news\n", "    search_depth: advanced\n", "    time_range: day\n", "    topic: news\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"recent ai news\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://www.bbc.com/news/articles/cn0q2v851k9o\", \"title\": \"Amazon boss says AI will replace jobs at tech giant - BBC\", \"score\": 0.71995544, \"published_date\": \"<PERSON><PERSON>, 17 Jun 2025 21:31:36 GMT\", \"content\": \"Companies, especially in the tech sector, have been investing heavily in AI in recent years, spurred on by technological advances that have made it easier than ever for chatbots to create code, images and text with limited instruction.\\n\\nBut as the new tools gain traction, they have sparked warnings from some tech leaders of job losses, especially in entry-level office roles. [...] <PERSON><PERSON>, chief executive of AI-firm Anthropic, told news website Axios last month that the technology could wipe out half of entry-level white collar jobs.\\n\\n<PERSON><PERSON><PERSON><PERSON>, whose work on AI, including at Google, has earned him the moniker \\\"Godfather of AI\\\", echoed those warnings on a recent podcast.\", \"raw_content\": null}, {\"url\": \"https://www.csoonline.com/article/4008912/wormgpt-returns-new-malicious-ai-variants-built-on-grok-and-mixtral-uncovered.html\", \"title\": \"WormGPT returns: New malicious AI variants built on Grok and Mixtral uncovered - csoonline.com\", \"score\": 0.70214266, \"published_date\": \"Wed, 18 Jun 2025 12:17:59 GMT\", \"content\": \"news ### New npm threats can erase production systems with a single request Jun 10, 2025 3 mins\\n   news ### Chrome extension privacy promises undone by hardcoded secrets, leaky HTTP Jun 9, 2025 4 mins\\n   news ### New phishing campaign hijacks clipboard via fake CAPTCHA for malware delivery Jun 6, 2025 4 mins\\n   news ### Supply chain attack hits RubyGems to steal Telegram API data Jun 5, 2025 4 mins [...] Related content\\n---------------\\n\\nOpinion ### Security, risk and compliance in the world of AI agents By Nikhil Sarnot Jun 17, 2025 12 mins Artificial Intelligence Compliance IT GovernanceNews ### Phishing goes prime time: Hackers use trusted sites to hijack search rankings By Shweta Sharma Jun 17, 2025 5 mins Phishing SecurityNews ### Malicious PyPI package targets Chimera users to steal AWS tokens, CI/CD secrets By Shweta Sharma Jun 17, 2025 4 mins Developer Malware Security [...] news ### ‘Grafana Ghost’ XSS flaw exposes 47,000 servers to account takeover Jun 16, 2025 3 mins\\n   news ### Fog ransomware gang abuses employee monitoring tool in unusual multi-stage attack Jun 13, 2025 4 mins\\n   news ### Phishing sites posing as DeepSeek downloads drop a proxy backdoor Jun 12, 2025 4 mins\\n   news ### China-linked hackers target cybersecurity firms, governments in global espionage campaign Jun 11, 2025 4 mins\", \"raw_content\": null}], \"response_time\": 1.43}\n"]}], "source": ["response=graph.invoke({\"messages\":\"Give me the recent ai news and then multiply 5 by 10\"})\n", "for m in response['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "28fd11d4", "metadata": {}, "source": ["### ReAct Agent Architecture"]}, {"cell_type": "code", "execution_count": 15, "id": "8676799c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## Stategraph\n", "from langgraph.graph import StateGraph,START,END\n", "from langgraph.prebuilt import ToolNode\n", "from langgraph.prebuilt import tools_condition\n", "\n", "## Node definition\n", "def tool_calling_llm(state:State):\n", "    return {\"messages\":[llm_with_tool.invoke(state[\"messages\"])]}\n", "\n", "## Grpah\n", "builder=StateGraph(State)\n", "builder.add_node(\"tool_calling_llm\",tool_calling_llm)\n", "builder.add_node(\"tools\",ToolNode(tools))\n", "\n", "## Add Edges\n", "builder.add_edge(START, \"tool_calling_llm\")\n", "builder.add_conditional_edges(\n", "    \"tool_calling_llm\",\n", "    # If the latest message (result) from assistant is a tool call -> tools_condition routes to tools\n", "    # If the latest message (result) from assistant is a not a tool call -> tools_condition routes to END\n", "    tools_condition\n", ")\n", "builder.add_edge(\"tools\",\"tool_calling_llm\")\n", "\n", "## compile the graph\n", "graph=builder.compile()\n", "\n", "from IPython.display import Image, display\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 19, "id": "9749684a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Give me the recent ai news and then multiply 5 by 10\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  tavily_search (sr15kq05g)\n", " Call ID: sr15kq05g\n", "  Args:\n", "    query: recent ai news\n", "    search_depth: advanced\n", "    time_range: day\n", "    topic: news\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"recent ai news\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://www.bbc.com/news/articles/cn0q2v851k9o\", \"title\": \"Amazon boss says AI will replace jobs at tech giant - BBC\", \"score\": 0.71995544, \"published_date\": \"<PERSON><PERSON>, 17 Jun 2025 21:31:36 GMT\", \"content\": \"Companies, especially in the tech sector, have been investing heavily in AI in recent years, spurred on by technological advances that have made it easier than ever for chatbots to create code, images and text with limited instruction.\\n\\nBut as the new tools gain traction, they have sparked warnings from some tech leaders of job losses, especially in entry-level office roles. [...] <PERSON><PERSON>, chief executive of AI-firm Anthropic, told news website Axios last month that the technology could wipe out half of entry-level white collar jobs.\\n\\n<PERSON><PERSON><PERSON><PERSON>, whose work on AI, including at Google, has earned him the moniker \\\"Godfather of AI\\\", echoed those warnings on a recent podcast.\", \"raw_content\": null}, {\"url\": \"https://www.csoonline.com/article/4008912/wormgpt-returns-new-malicious-ai-variants-built-on-grok-and-mixtral-uncovered.html\", \"title\": \"WormGPT returns: New malicious AI variants built on Grok and Mixtral uncovered - csoonline.com\", \"score\": 0.70214266, \"published_date\": \"Wed, 18 Jun 2025 12:17:59 GMT\", \"content\": \"news ### New npm threats can erase production systems with a single request Jun 10, 2025 3 mins\\n   news ### Chrome extension privacy promises undone by hardcoded secrets, leaky HTTP Jun 9, 2025 4 mins\\n   news ### New phishing campaign hijacks clipboard via fake CAPTCHA for malware delivery Jun 6, 2025 4 mins\\n   news ### Supply chain attack hits RubyGems to steal Telegram API data Jun 5, 2025 4 mins [...] Related content\\n---------------\\n\\nOpinion ### Security, risk and compliance in the world of AI agents By Nikhil Sarnot Jun 17, 2025 12 mins Artificial Intelligence Compliance IT GovernanceNews ### Phishing goes prime time: Hackers use trusted sites to hijack search rankings By Shweta Sharma Jun 17, 2025 5 mins Phishing SecurityNews ### Malicious PyPI package targets Chimera users to steal AWS tokens, CI/CD secrets By Shweta Sharma Jun 17, 2025 4 mins Developer Malware Security [...] news ### ‘Grafana Ghost’ XSS flaw exposes 47,000 servers to account takeover Jun 16, 2025 3 mins\\n   news ### Fog ransomware gang abuses employee monitoring tool in unusual multi-stage attack Jun 13, 2025 4 mins\\n   news ### Phishing sites posing as DeepSeek downloads drop a proxy backdoor Jun 12, 2025 4 mins\\n   news ### China-linked hackers target cybersecurity firms, governments in global espionage campaign Jun 11, 2025 4 mins\", \"raw_content\": null}], \"response_time\": 2.72}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Based on the output of tool call id \"sr15kq05g\", I will now multiply 5 by 10.\n", "\n", "The result of the multiplication is:\n", "\n", "50\n"]}], "source": ["response=graph.invoke({\"messages\":\"Give me the recent ai news and then multiply 5 by 10\"})\n", "for m in response['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "3fb80059", "metadata": {}, "source": ["## Adding Memory In Agentic Graph"]}, {"cell_type": "code", "execution_count": 16, "id": "0249955f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hello my name is <PERSON><PERSON><PERSON>\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Nice to meet you, <PERSON><PERSON><PERSON>! How are you today?\n"]}], "source": ["response=graph.invoke({\"messages\":\"Hello my name is <PERSON><PERSON><PERSON>\"})\n", "for m in response['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": 17, "id": "e852099b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is my name\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  multiply (xgm8j8jmw)\n", " Call ID: xgm8j8jmw\n", "  Args:\n", "    a: 1\n", "    b: 0\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: multiply\n", "\n", "0\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I apologize for the mistake earlier. Since the tool call id \"xgm8j8jmw\" yielded 0, I will assume you are asking about your name again.\n", "\n", "Unfortunately, I don't have any information about your name, as it's not provided in the conversation. Can you please provide more context or clarify what you mean by \"my name\"? I'll do my best to help.\n"]}], "source": ["response=graph.invoke({\"messages\":\"What is my name\"})\n", "for m in response['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": 24, "id": "4bf6c416", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["## Stategraph\n", "from langgraph.graph import StateGraph,START,END\n", "from langgraph.prebuilt import ToolNode\n", "from langgraph.prebuilt import tools_condition\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "memory = MemorySaver()\n", "\n", "## Node definition\n", "def tool_calling_llm(state:State):\n", "    return {\"messages\":[llm_with_tool.invoke(state[\"messages\"])]}\n", "\n", "## Grpah\n", "builder=StateGraph(State)\n", "builder.add_node(\"tool_calling_llm\",tool_calling_llm)\n", "builder.add_node(\"tools\",ToolNode(tools))\n", "\n", "## Add Edges\n", "builder.add_edge(START, \"tool_calling_llm\")\n", "builder.add_conditional_edges(\n", "    \"tool_calling_llm\",\n", "    # If the latest message (result) from assistant is a tool call -> tools_condition routes to tools\n", "    # If the latest message (result) from assistant is a not a tool call -> tools_condition routes to END\n", "    tools_condition\n", ")\n", "builder.add_edge(\"tools\",\"tool_calling_llm\")\n", "\n", "## compile the graph\n", "graph=builder.compile(checkpointer=memory)\n", "\n", "from IPython.display import Image, display\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 25, "id": "426b9240", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Hi my name is <PERSON><PERSON>', additional_kwargs={}, response_metadata={}, id='e55bf5c4-e660-4919-ab56-053030ab687d'),\n", "  AIMessage(content='Nice to meet you, <PERSON><PERSON>! Is there something I can help you with or would you like to chat?', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 23, 'prompt_tokens': 2193, 'total_tokens': 2216, 'completion_time': 0.033208577, 'prompt_time': 0.489441871, 'queue_time': 3.1984534190000002, 'total_time': 0.522650448}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8b7c3a83f7', 'finish_reason': 'stop', 'logprobs': None}, id='run--b6cac6ef-4b1a-4ece-b1ed-fad73ad85355-0', usage_metadata={'input_tokens': 2193, 'output_tokens': 23, 'total_tokens': 2216})]}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["config={\"configurable\":{\"thread_id\":\"1\"}}\n", "\n", "response=graph.invoke({\"messages\":\"Hi my name is <PERSON><PERSON>\"},config=config)\n", "\n", "response\n", "\n"]}, {"cell_type": "code", "execution_count": 27, "id": "904463e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Nice to meet you, <PERSON><PERSON>! Is there something I can help you with or would you like to chat?'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["response['messages'][-1].content"]}, {"cell_type": "code", "execution_count": 28, "id": "363227cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Your name is <PERSON><PERSON>.\n"]}], "source": ["response=graph.invoke({\"messages\":\"Hey what is my name\"},config=config)\n", "\n", "print(response['messages'][-1].content)"]}, {"cell_type": "code", "execution_count": 30, "id": "d53567d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Your name is <PERSON><PERSON>, right?\n"]}], "source": ["response=graph.invoke({\"messages\":\"Hey do you remember mmy name\"},config=config)\n", "\n", "print(response['messages'][-1].content)"]}, {"cell_type": "markdown", "id": "ac7b3d4d", "metadata": {}, "source": ["### Streaming"]}, {"cell_type": "code", "execution_count": 44, "id": "29fe2fbf", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "memory=MemorySaver()"]}, {"cell_type": "code", "execution_count": 45, "id": "5bca6fc4", "metadata": {}, "outputs": [], "source": ["def superbot(state:State):\n", "    return {\"messages\":[llm.invoke(state['messages'])]}"]}, {"cell_type": "code", "execution_count": 46, "id": "433922c6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["graph=StateGraph(State)\n", "\n", "## node\n", "graph.add_node(\"SuperBot\",superbot)\n", "## Edges\n", "\n", "graph.add_edge(START,\"SuperBot\")\n", "graph.add_edge(\"SuperBot\",END)\n", "\n", "\n", "graph_builder=graph.compile(checkpointer=memory)\n", "\n", "\n", "## Display\n", "from IPython.display import Image, display\n", "display(Image(graph_builder.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 47, "id": "486b24f1", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Hi,My name is <PERSON><PERSON> And I like cricket', additional_kwargs={}, response_metadata={}, id='4bbd93a6-5c99-476d-af9b-2542f400b2be'),\n", "  AIMessage(content=\"<PERSON> <PERSON><PERSON>! Nice to meet you! Cricket is a great sport, isn't it? Who's your favorite cricketer or team? Do you play cricket yourself or just enjoy watching and following the game?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 43, 'prompt_tokens': 20, 'total_tokens': 63, 'completion_time': 0.103689851, 'prompt_time': 0.017182506, 'queue_time': 3.3513802249999998, 'total_time': 0.120872357}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--46e8c88b-782a-438f-9ccc-58ab8668c0bf-0', usage_metadata={'input_tokens': 20, 'output_tokens': 43, 'total_tokens': 63})]}"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["## Invocation\n", "\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "graph_builder.invoke({'messages':\"<PERSON>,My name is <PERSON><PERSON> And I like cricket\"},config)"]}, {"cell_type": "markdown", "id": "2b14a6a0", "metadata": {}, "source": ["### Streaming \n", "Methods: .stream() and astream()\n", "\n", "- These methods are sync and async methods for streaming back results.\n", "\n", "Additional parameters in streaming modes for graph state\n", "\n", "- **values** : This streams the full state of the graph after each node is called.\n", "- **updates** : This streams updates to the state of the graph after each node is called."]}, {"cell_type": "code", "execution_count": null, "id": "32d6ac91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'SuperBot': {'messages': [AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! It's great to hear that you like cricket! What's your favorite team or player in cricket?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 28, 'prompt_tokens': 20, 'total_tokens': 48, 'completion_time': 0.067597787, 'prompt_time': 0.017155746, 'queue_time': 3.961062715, 'total_time': 0.084753533}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--dd517fd5-951c-4ba1-9af4-e5eddab1c545-0', usage_metadata={'input_tokens': 20, 'output_tokens': 28, 'total_tokens': 48})]}}\n"]}], "source": ["# Create a thread\n", "config = {\"configurable\": {\"thread_id\": \"3\"}}\n", "\n", "for chunk in graph_builder.stream({'messages':\"<PERSON>,My name is <PERSON><PERSON> And I like cricket\"},config,stream_mode=\"updates\"):\n", "    print(chunk)"]}, {"cell_type": "code", "execution_count": 49, "id": "2fefc7c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'messages': [HumanMessage(content='Hi,My name is <PERSON><PERSON> And I like cricket', additional_kwargs={}, response_metadata={}, id='2bdefe18-5571-41a9-9c62-59f9b739b6b6'), AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! It's great to hear that you like cricket! What's your favorite team or player in cricket?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 28, 'prompt_tokens': 20, 'total_tokens': 48, 'completion_time': 0.067597787, 'prompt_time': 0.017155746, 'queue_time': 3.961062715, 'total_time': 0.084753533}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--dd517fd5-951c-4ba1-9af4-e5eddab1c545-0', usage_metadata={'input_tokens': 20, 'output_tokens': 28, 'total_tokens': 48}), HumanMessage(content='Hi,My name is <PERSON><PERSON> And I like cricket', additional_kwargs={}, response_metadata={}, id='12985285-9873-4723-82c7-3864b7f001e4')]}\n", "{'messages': [HumanMessage(content='Hi,My name is <PERSON><PERSON> And I like cricket', additional_kwargs={}, response_metadata={}, id='2bdefe18-5571-41a9-9c62-59f9b739b6b6'), AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! It's great to hear that you like cricket! What's your favorite team or player in cricket?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 28, 'prompt_tokens': 20, 'total_tokens': 48, 'completion_time': 0.067597787, 'prompt_time': 0.017155746, 'queue_time': 3.961062715, 'total_time': 0.084753533}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--dd517fd5-951c-4ba1-9af4-e5eddab1c545-0', usage_metadata={'input_tokens': 20, 'output_tokens': 28, 'total_tokens': 48}), HumanMessage(content='Hi,My name is <PERSON><PERSON> And I like cricket', additional_kwargs={}, response_metadata={}, id='12985285-9873-4723-82c7-3864b7f001e4'), AIMessage(content=\"Nice to meet you, Krish! It's great to hear that you like cricket! What's your favorite team or player in cricket?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 28, 'prompt_tokens': 67, 'total_tokens': 95, 'completion_time': 0.046937595, 'prompt_time': 0.025860729, 'queue_time': 0.376788149, 'total_time': 0.072798324}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--1b91f3ce-46fa-4840-855e-87b9b2d004db-0', usage_metadata={'input_tokens': 67, 'output_tokens': 28, 'total_tokens': 95})]}\n"]}], "source": ["for chunk in graph_builder.stream({'messages':\"<PERSON>,My name is <PERSON><PERSON> And I like cricket\"},config,stream_mode=\"values\"):\n", "    print(chunk)"]}, {"cell_type": "code", "execution_count": 50, "id": "2306108c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'SuperBot': {'messages': [AIMessage(content=\"Hello <PERSON><PERSON>! Nice to meet you! It's great to know that you like cricket! Which team do you support?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 25, 'prompt_tokens': 20, 'total_tokens': 45, 'completion_time': 0.041678799, 'prompt_time': 0.017140167, 'queue_time': 5.561800282, 'total_time': 0.058818966}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--e14c0aff-5759-4df1-9ec3-def7a3a150dd-0', usage_metadata={'input_tokens': 20, 'output_tokens': 25, 'total_tokens': 45})]}}\n"]}], "source": ["# Create a thread\n", "config = {\"configurable\": {\"thread_id\": \"4\"}}\n", "\n", "for chunk in graph_builder.stream({'messages':\"<PERSON>,My name is <PERSON><PERSON> And I like cricket\"},config,stream_mode=\"updates\"):\n", "    print(chunk)"]}, {"cell_type": "code", "execution_count": 51, "id": "321c88bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'messages': [HumanMessage(content='Hi,My name is <PERSON><PERSON> And I like cricket', additional_kwargs={}, response_metadata={}, id='28871637-a931-4c6d-a84b-5219d4aaf9c5'), AIMessage(content=\"Hello <PERSON><PERSON>! Nice to meet you! It's great to know that you like cricket! Which team do you support?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 25, 'prompt_tokens': 20, 'total_tokens': 45, 'completion_time': 0.041678799, 'prompt_time': 0.017140167, 'queue_time': 5.561800282, 'total_time': 0.058818966}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--e14c0aff-5759-4df1-9ec3-def7a3a150dd-0', usage_metadata={'input_tokens': 20, 'output_tokens': 25, 'total_tokens': 45}), HumanMessage(content='I also like football', additional_kwargs={}, response_metadata={}, id='cdc89c65-01b1-4ccf-ab6a-48aed71fefd4')]}\n", "{'messages': [HumanMessage(content='Hi,My name is <PERSON><PERSON> And I like cricket', additional_kwargs={}, response_metadata={}, id='28871637-a931-4c6d-a84b-5219d4aaf9c5'), AIMessage(content=\"Hello <PERSON><PERSON>! Nice to meet you! It's great to know that you like cricket! Which team do you support?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 25, 'prompt_tokens': 20, 'total_tokens': 45, 'completion_time': 0.041678799, 'prompt_time': 0.017140167, 'queue_time': 5.561800282, 'total_time': 0.058818966}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--e14c0aff-5759-4df1-9ec3-def7a3a150dd-0', usage_metadata={'input_tokens': 20, 'output_tokens': 25, 'total_tokens': 45}), HumanMessage(content='I also like football', additional_kwargs={}, response_metadata={}, id='cdc89c65-01b1-4ccf-ab6a-48aed71fefd4'), AIMessage(content=\"A sports fan with diverse interests! That's awesome, Krish! Football and cricket are two of the most popular sports in the world, and it's great that you enjoy both.\\n\\nDo you have a favorite football team or player? And similarly, do you have a favorite cricketer or team?\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 61, 'prompt_tokens': 58, 'total_tokens': 119, 'completion_time': 0.096853191, 'prompt_time': 0.046696348, 'queue_time': 8.785236603000001, 'total_time': 0.143549539}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e', 'finish_reason': 'stop', 'logprobs': None}, id='run--2e1e33f8-2348-432d-bf8c-dc08033d3ff1-0', usage_metadata={'input_tokens': 58, 'output_tokens': 61, 'total_tokens': 119})]}\n"]}], "source": ["for chunk in graph_builder.stream({'messages':\"I also like football\"},config,stream_mode=\"values\"):\n", "    print(chunk)"]}, {"cell_type": "code", "execution_count": 52, "id": "57e296a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'event': 'on_chain_start', 'data': {'input': {'messages': ['Hi My name is <PERSON><PERSON> and I like to play cricket']}}, 'name': 'LangGraph', 'tags': [], 'run_id': 'fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', 'metadata': {'thread_id': '5'}, 'parent_ids': []}\n", "{'event': 'on_chain_start', 'data': {'input': {'messages': [HumanMessage(content='Hi My name is <PERSON><PERSON> and I like to play cricket', additional_kwargs={}, response_metadata={}, id='5cbc8727-5339-4a8a-9d63-09267608e1af')]}}, 'name': 'SuperBot', 'tags': ['graph:step:1'], 'run_id': '8108e5d5-e6eb-44a0-8057-41b3e38e8eef', 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa'}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6']}\n", "{'event': 'on_chat_model_start', 'data': {'input': {'messages': [[HumanMessage(content='Hi My name is <PERSON><PERSON> and I like to play cricket', additional_kwargs={}, response_metadata={}, id='5cbc8727-5339-4a8a-9d63-09267608e1af')]]}}, 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='Nice', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' to', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' meet', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' you', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=',', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' Krish', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='!', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' That', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=\"'s\", additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' awesome', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' that', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' you', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' like', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' to', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' play', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' cricket', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='!', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' What', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=\"'s\", additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' your', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' favorite', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' aspect', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' of', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' the', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' game', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='?', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' Are', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' you', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' a', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' skilled', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' bats', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='man', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=',', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' a', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' sharp', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' bow', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='ler', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=',', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' or', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' a', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' agile', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' field', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='er', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='?', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' Do', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' you', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' play', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' for', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' a', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' team', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' or', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' with', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' friends', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='?', additional_kwargs={}, response_metadata={}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b')}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e'}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b', usage_metadata={'input_tokens': 21, 'output_tokens': 55, 'total_tokens': 76})}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chat_model_end', 'data': {'output': AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! That's awesome that you like to play cricket! What's your favorite aspect of the game? Are you a skilled batsman, a sharp bowler, or a agile fielder? Do you play for a team or with friends?\", additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e'}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b', usage_metadata={'input_tokens': 21, 'output_tokens': 55, 'total_tokens': 76}), 'input': {'messages': [[HumanMessage(content='Hi My name is <PERSON><PERSON> and I like to play cricket', additional_kwargs={}, response_metadata={}, id='5cbc8727-5339-4a8a-9d63-09267608e1af')]]}}, 'run_id': 'df1ba13a-42a0-4ccc-b173-a881ab65ed1b', 'name': 'ChatGroq', 'tags': ['seq:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa', 'ls_provider': 'groq', 'ls_model_name': 'llama3-8b-8192', 'ls_model_type': 'chat', 'ls_temperature': 0.7}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', '8108e5d5-e6eb-44a0-8057-41b3e38e8eef']}\n", "{'event': 'on_chain_stream', 'run_id': '8108e5d5-e6eb-44a0-8057-41b3e38e8eef', 'name': 'SuperBot', 'tags': ['graph:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa'}, 'data': {'chunk': {'messages': [AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! That's awesome that you like to play cricket! What's your favorite aspect of the game? Are you a skilled batsman, a sharp bowler, or a agile fielder? Do you play for a team or with friends?\", additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e'}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b', usage_metadata={'input_tokens': 21, 'output_tokens': 55, 'total_tokens': 76})]}}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6']}\n", "{'event': 'on_chain_end', 'data': {'output': {'messages': [AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! That's awesome that you like to play cricket! What's your favorite aspect of the game? Are you a skilled batsman, a sharp bowler, or a agile fielder? Do you play for a team or with friends?\", additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e'}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b', usage_metadata={'input_tokens': 21, 'output_tokens': 55, 'total_tokens': 76})]}, 'input': {'messages': [HumanMessage(content='Hi My name is <PERSON><PERSON> and I like to play cricket', additional_kwargs={}, response_metadata={}, id='5cbc8727-5339-4a8a-9d63-09267608e1af')]}}, 'run_id': '8108e5d5-e6eb-44a0-8057-41b3e38e8eef', 'name': 'SuperBot', 'tags': ['graph:step:1'], 'metadata': {'thread_id': '5', 'langgraph_step': 1, 'langgraph_node': 'SuperBot', 'langgraph_triggers': ('branch:to:SuperBot',), 'langgraph_path': ('__pregel_pull', 'SuperBot'), 'langgraph_checkpoint_ns': 'SuperBot:81dc6040-44c3-d3b6-96a8-292b5d7a3caa'}, 'parent_ids': ['fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6']}\n", "{'event': 'on_chain_stream', 'run_id': 'fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', 'name': 'LangGraph', 'tags': [], 'metadata': {'thread_id': '5'}, 'data': {'chunk': {'SuperBot': {'messages': [AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! That's awesome that you like to play cricket! What's your favorite aspect of the game? Are you a skilled batsman, a sharp bowler, or a agile fielder? Do you play for a team or with friends?\", additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e'}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b', usage_metadata={'input_tokens': 21, 'output_tokens': 55, 'total_tokens': 76})]}}}, 'parent_ids': []}\n", "{'event': 'on_chain_end', 'data': {'output': {'messages': [HumanMessage(content='Hi My name is <PERSON><PERSON> and I like to play cricket', additional_kwargs={}, response_metadata={}, id='5cbc8727-5339-4a8a-9d63-09267608e1af'), AIMessage(content=\"Nice to meet you, <PERSON><PERSON>! That's awesome that you like to play cricket! What's your favorite aspect of the game? Are you a skilled batsman, a sharp bowler, or a agile fielder? Do you play for a team or with friends?\", additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_8dc6ecaf8e'}, id='run--df1ba13a-42a0-4ccc-b173-a881ab65ed1b', usage_metadata={'input_tokens': 21, 'output_tokens': 55, 'total_tokens': 76})]}}, 'run_id': 'fc9179e8-7f2a-40ea-86d8-80d86b0ab3b6', 'name': 'LangGraph', 'tags': [], 'metadata': {'thread_id': '5'}, 'parent_ids': []}\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"5\"}}\n", "\n", "async for event in graph_builder.astream_events({\"messages\":[\"Hi My name is <PERSON><PERSON> and I like to play cricket\"]},config,version=\"v2\"):\n", "    print(event)"]}, {"cell_type": "code", "execution_count": null, "id": "b4326d9b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "AgenticLanggraph", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}