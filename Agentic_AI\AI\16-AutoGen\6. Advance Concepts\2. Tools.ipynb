{"cells": [{"cell_type": "code", "execution_count": null, "id": "42e341b6", "metadata": {}, "outputs": [], "source": ["from langchain_community.utilities import GoogleSerperAPIWrapper\n", "import os\n", "import pprint\n", "\n", "os.environ[\"SERPER_API_KEY\"] = \"339419d43108cfbf028b86f3705ad0284f04199a\"\n", "\n", "serach_tool = GoogleSerperAPIWrapper()\n", "def search_web(query: str):\n", "    return serach_tool.run(query)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}