class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

def mergeTwoLists(l1, l2):
    dummy = ListNode(0)
    current = dummy
    
    while l1 and l2:
        if l1.val < l2.val:
            current.next = l1
            l1 = l1.next
        else:
            current.next = l2
            l2 = l2.next
        current = current.next
    
    # If one of the lists is not finished, append it
    if l1:
        current.next = l1
    elif l2:
        current.next = l2
    
    return dummy.next

# Test cases
def print_list(node):
    result = []
    while node:
        result.append(node.val)
        node = node.next
    return result

# Create linked lists for testing
# Test Case 1
l1 = ListNode(1, ListNode(2, ListNode(4)))
l2 = ListNode(1, ListNode(3, ListNode(4)))

# Test Case 2
l1_test2 = ListNode(2, ListNode(6, ListNode(8)))
l2_test2 = ListNode(1, ListNode(3, ListNode(5)))

# Test Case 3
l1_test3 = ListNode(1)
l2_test3 = ListNode(2)

merged1 = mergeTwoLists(l1, l2)
merged2 = mergeTwoLists(l1_test2, l2_test2)
merged3 = mergeTwoLists(l1_test3, l2_test3)

print(print_list(merged1))
print(print_list(merged2))
print(print_list(merged3))
